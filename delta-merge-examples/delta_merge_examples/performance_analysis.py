"""
Comprehensive performance analysis with profiling and optimization recommendations.
"""

import os
import shutil
import random
from pathlib import Path
import pyarrow as pa
import pyarrow.parquet as pq
import duckdb
from deltalake import DeltaTable, write_deltalake

from delta_merge_examples.shared_data_generator import (
    ParquetDatasetManager, 
    generate_transaction_data
)
from delta_merge_examples.performance_profiler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
    DuckLakeProfiler, 
    compare_profiling_results
)


class PerformanceAnalysis:
    """Comprehensive performance analysis with profiling and optimization testing."""
    
    def __init__(self):
        self.dataset_manager = ParquetDatasetManager()
        self.delta_table_path = "./perf_analysis_delta"
        self.ducklake_path = "./perf_analysis_ducklake"
        self.delta_profiler = None
        self.ducklake_profiler = None
        self.ducklake_conn = None
    
    def setup_test_environment(self, use_small_dataset: bool = False):
        """Set up test environment with profiling-ready datasets."""
        print("🏗️  Setting up performance analysis environment...")
        
        # Clean up existing tables
        self.cleanup()
        
        # Use smaller dataset for detailed profiling
        if use_small_dataset:
            dataset_name = "perf_test_100k"
            total_files = 200
            total_rows = 100000
        else:
            dataset_name = "perf_test_1m"
            total_files = 2000
            total_rows = 1000000
        
        # Ensure dataset exists
        if not self.dataset_manager.dataset_exists(dataset_name):
            print(f"📦 Generating {dataset_name} dataset...")
            self.dataset_manager.generate_dataset(
                dataset_name=dataset_name,
                total_files=total_files,
                total_rows=total_rows,
                large_file_ratio=1/50
            )
        
        metadata = self.dataset_manager.load_dataset_metadata(dataset_name)
        
        # Setup Delta Lake
        print("🔺 Setting up Delta Lake...")
        self.setup_delta_table(metadata)
        
        # Setup DuckLake
        print("🦆 Setting up DuckLake...")
        self.setup_ducklake_table(metadata)
        
        return metadata
    
    def setup_delta_table(self, metadata):
        """Set up Delta table from Parquet dataset."""
        # Load data in batches
        batch_size = 50
        total_files = len(metadata['parquet_files'])
        
        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            batch_files = metadata['parquet_files'][batch_start:batch_end]
            parquet_paths = [f['parquet_path'] for f in batch_files]
            
            # Read and combine Parquet files
            tables = [pq.read_table(path) for path in parquet_paths]
            combined_table = pa.concat_tables(tables)
            
            # Write to Delta table
            mode = "overwrite" if batch_start == 0 else "append"
            write_deltalake(self.delta_table_path, combined_table, mode=mode)
        
        # Initialize profiler
        self.delta_profiler = DeltaLakeProfiler(self.delta_table_path)
        
        dt = DeltaTable(self.delta_table_path)
        print(f"✅ Delta table ready with {dt.to_pyarrow_table().num_rows:,} rows")
    
    def setup_ducklake_table(self, metadata):
        """Set up DuckLake table from Parquet dataset."""
        # Initialize DuckDB connection
        self.ducklake_conn = duckdb.connect()
        self.ducklake_conn.execute("INSTALL ducklake;")
        self.ducklake_conn.execute("LOAD ducklake;")
        self.ducklake_conn.execute(f"ATTACH 'ducklake:{self.ducklake_path}' AS my_ducklake;")
        self.ducklake_conn.execute("USE my_ducklake;")
        
        # Create table schema
        self.ducklake_conn.execute("""
            CREATE TABLE demo (
                file_id VARCHAR,
                customer_id VARCHAR,
                transaction_id VARCHAR,
                amount DOUBLE,
                transaction_date TIMESTAMP,
                processed_at TIMESTAMP
            );
        """)
        
        # Load data from Parquet files
        batch_size = 50
        total_files = len(metadata['parquet_files'])
        
        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            batch_files = metadata['parquet_files'][batch_start:batch_end]
            
            for file_info in batch_files:
                parquet_path = file_info['parquet_path']
                self.ducklake_conn.execute(f"INSERT INTO demo SELECT * FROM read_parquet('{parquet_path}');")
        
        # Initialize profiler
        self.ducklake_profiler = DuckLakeProfiler(self.ducklake_conn)
        
        result = self.ducklake_conn.execute("SELECT COUNT(*) FROM demo;").fetchone()
        total_rows = result[0] if result else 0
        print(f"✅ DuckLake table ready with {total_rows:,} rows")
    
    def profile_delta_merge_performance(self, metadata):
        """Profile Delta Lake MERGE operation with detailed breakdown."""
        print(f"\n{'='*80}")
        print("DELTA LAKE MERGE PROFILING")
        print(f"{'='*80}")
        
        dt = DeltaTable(self.delta_table_path)
        
        # Analyze table structure first
        self.delta_profiler.analyze_table_structure(dt)
        
        # Find a large file to update
        large_files = [f for f in metadata['parquet_files'] if f['is_large']]
        if not large_files:
            print("❌ No large files found for testing")
            return
        
        target_file_info = random.choice(large_files)
        target_file = target_file_info['file_id']
        original_size = target_file_info['row_count']
        new_size = max(1000, int(original_size * 0.9))  # Slightly smaller
        
        print(f"🎯 Testing with file: {target_file} ({original_size:,} → {new_size:,} rows)")
        
        # Generate new data
        new_df = generate_transaction_data(target_file, new_size)
        new_data = pa.Table.from_pandas(new_df)
        
        # Profile the MERGE operation
        result = self.delta_profiler.profile_merge_operation(dt, new_data, target_file)
        
        # Print detailed profile
        self.delta_profiler.print_profile_summary()
        
        return result
    
    def profile_delta_native_performance(self, metadata):
        """Profile Delta Lake native operations with detailed breakdown."""
        print(f"\n{'='*80}")
        print("DELTA LAKE NATIVE OPERATIONS PROFILING")
        print(f"{'='*80}")
        
        dt = DeltaTable(self.delta_table_path)
        
        # Find a different large file to update
        large_files = [f for f in metadata['parquet_files'] if f['is_large']]
        if len(large_files) < 2:
            print("❌ Need at least 2 large files for testing")
            return
        
        target_file_info = large_files[1]  # Use second large file
        target_file = target_file_info['file_id']
        original_size = target_file_info['row_count']
        new_size = max(1000, int(original_size * 0.9))
        
        print(f"🎯 Testing with file: {target_file} ({original_size:,} → {new_size:,} rows)")
        
        # Generate new data
        new_df = generate_transaction_data(target_file, new_size)
        new_data = pa.Table.from_pandas(new_df)
        
        # Profile native operations
        result = self.delta_profiler.profile_native_operations(dt, new_data, target_file)
        
        # Print detailed profile
        self.delta_profiler.print_profile_summary()
        
        return result
    
    def profile_ducklake_performance(self, metadata):
        """Profile DuckLake operations with EXPLAIN ANALYZE."""
        print(f"\n{'='*80}")
        print("DUCKLAKE OPERATIONS PROFILING")
        print(f"{'='*80}")
        
        # Analyze table structure
        self.ducklake_profiler.analyze_table_structure()
        
        # Find a large file to update
        large_files = [f for f in metadata['parquet_files'] if f['is_large']]
        if not large_files:
            print("❌ No large files found for testing")
            return
        
        target_file_info = random.choice(large_files)
        target_file = target_file_info['file_id']
        original_size = target_file_info['row_count']
        new_size = max(1000, int(original_size * 0.9))
        
        print(f"🎯 Testing with file: {target_file} ({original_size:,} → {new_size:,} rows)")
        
        # Generate new data and register it
        new_df = generate_transaction_data(target_file, new_size)
        self.ducklake_conn.register('new_data', new_df)
        
        # Profile DELETE operation
        delete_query = f"DELETE FROM demo WHERE file_id = '{target_file}'"
        self.ducklake_profiler.profile_sql_operation("DELETE", delete_query, explain=True)
        
        # Profile INSERT operation
        insert_query = "INSERT INTO demo SELECT * FROM new_data"
        self.ducklake_profiler.profile_sql_operation("INSERT", insert_query, explain=True)
        
        # Profile combined transaction
        transaction_queries = [
            f"DELETE FROM demo WHERE file_id = '{target_file}_tx'",
            "INSERT INTO demo SELECT * FROM new_data"
        ]
        self.ducklake_profiler.profile_transaction_operation("UPDATE", transaction_queries)
        
        # Clean up
        self.ducklake_conn.unregister('new_data')
        
        # Print detailed profile
        self.ducklake_profiler.print_profile_summary()
    
    def analyze_optimization_opportunities(self):
        """Analyze optimization opportunities for both systems."""
        print(f"\n{'='*80}")
        print("OPTIMIZATION ANALYSIS")
        print(f"{'='*80}")
        
        print("🔍 Analyzing Delta Lake optimization opportunities...")
        self.analyze_delta_optimizations()
        
        print("\n🔍 Analyzing DuckLake optimization opportunities...")
        self.analyze_ducklake_optimizations()
        
        print("\n🔍 Comparing profiling results...")
        compare_profiling_results(self.delta_profiler, self.ducklake_profiler)
    
    def analyze_delta_optimizations(self):
        """Analyze Delta Lake specific optimizations."""
        dt = DeltaTable(self.delta_table_path)
        
        print("📊 Delta Lake optimization analysis:")
        
        # File count analysis
        files = dt.files()
        print(f"  📄 Current file count: {len(files)}")
        if len(files) > 100:
            print("  ⚠️  High file count detected - consider OPTIMIZE")
        
        # Partitioning analysis
        metadata = dt.metadata()
        if not metadata.partition_columns:
            print("  🗂️  No partitioning - consider partitioning by file_id for better performance")
        
        # Version analysis
        version = dt.version()
        print(f"  🔢 Current version: {version}")
        if version > 50:
            print("  ⚠️  High version count - consider VACUUM to clean up old versions")
        
        # Schema analysis
        schema = dt.schema()
        try:
            column_names = [field.name for field in schema.fields]
            print(f"  📋 Schema columns: {column_names}")
        except:
            print(f"  📋 Schema: {schema}")
        
        # Suggest Z-ORDER optimization
        print("  🎯 Suggested optimizations:")
        print("    - OPTIMIZE table for file compaction")
        print("    - Z-ORDER BY file_id for better file pruning")
        print("    - Consider partitioning by date or file_id")
        print("    - Add bloom filters for file_id column")
    
    def analyze_ducklake_optimizations(self):
        """Analyze DuckLake specific optimizations."""
        print("📊 DuckLake optimization analysis:")
        
        # Index analysis
        try:
            result = self.ducklake_conn.execute("SHOW TABLES;").fetchall()
            print(f"  📋 Tables: {[row[0] for row in result]}")
            
            # Check for indexes
            try:
                result = self.ducklake_conn.execute("PRAGMA show_indexes;").fetchall()
                if result:
                    print(f"  🗂️  Indexes: {result}")
                else:
                    print("  🗂️  No indexes found - consider adding index on file_id")
            except:
                print("  🗂️  Could not check indexes")
            
        except Exception as e:
            print(f"  ⚠️  Could not analyze table structure: {e}")
        
        print("  🎯 Suggested optimizations:")
        print("    - CREATE INDEX ON demo(file_id) for faster lookups")
        print("    - Consider table partitioning if supported")
        print("    - Use prepared statements for repeated queries")
        print("    - Batch operations in transactions")
    
    def cleanup(self):
        """Clean up test resources."""
        try:
            if os.path.exists(self.delta_table_path):
                shutil.rmtree(self.delta_table_path)
        except:
            pass
        
        try:
            if self.ducklake_conn:
                self.ducklake_conn.close()
        except:
            pass
        
        try:
            if os.path.exists(self.ducklake_path):
                if os.path.isfile(self.ducklake_path):
                    os.remove(self.ducklake_path)
                else:
                    shutil.rmtree(self.ducklake_path)
        except:
            pass


def run_performance_analysis():
    """Run comprehensive performance analysis."""
    analyzer = PerformanceAnalysis()
    
    try:
        # Setup with smaller dataset for detailed profiling
        metadata = analyzer.setup_test_environment(use_small_dataset=True)
        
        # Profile Delta Lake MERGE
        analyzer.profile_delta_merge_performance(metadata)
        
        # Profile Delta Lake native operations
        analyzer.profile_delta_native_performance(metadata)
        
        # Profile DuckLake operations
        analyzer.profile_ducklake_performance(metadata)
        
        # Analyze optimization opportunities
        analyzer.analyze_optimization_opportunities()
        
    finally:
        analyzer.cleanup()


if __name__ == "__main__":
    run_performance_analysis()
