"""
Scale analysis for Delta Lake file replacement approaches.
Tests performance at different data scales and analyzes potential problems.
"""

import os
import shutil
import time
import psutil
from pathlib import Path
import pyarrow as pa
from deltalake import DeltaTable, write_deltalake
from delta_merge_examples.data_generator import create_initial_table_data, create_large_file_data
from delta_merge_examples.acid_analysis import ACIDAnalysis


class ScaleAnalysis(ACIDAnalysis):
    """Analyze performance and behavior at different scales."""
    
    def __init__(self, table_path: str = "./scale_test_table"):
        super().__init__(table_path)
        self.results = []
    
    def setup_large_table(self, num_files: int = 10, rows_per_file: int = 1000):
        """Create a larger initial table for scale testing."""
        if os.path.exists(self.table_path):
            shutil.rmtree(self.table_path)
        
        print(f"🏗️  Creating large table: {num_files} files × {rows_per_file} rows = {num_files * rows_per_file:,} total rows")
        
        # Create data for multiple files
        all_data = []
        for i in range(num_files):
            file_id = f"file_{chr(65 + i)}"  # file_A, file_B, etc.
            file_data = create_large_file_data(file_id, rows_per_file)
            all_data.append(file_data.to_pandas())
        
        # Combine all data
        import pandas as pd
        combined_df = pd.concat(all_data, ignore_index=True)
        combined_table = pa.Table.from_pandas(combined_df)
        
        # Write to Delta table
        write_deltalake(self.table_path, combined_table, mode="overwrite")
        
        dt = DeltaTable(self.table_path)
        print(f"✅ Created table with {dt.to_pyarrow_table().num_rows:,} rows")
        return dt
    
    def measure_system_resources(self):
        """Measure current system resource usage."""
        process = psutil.Process()
        return {
            "memory_mb": process.memory_info().rss / 1024 / 1024,
            "cpu_percent": process.cpu_percent(),
            "disk_usage_mb": sum(f.stat().st_size for f in Path(self.table_path).rglob('*') if f.is_file()) / 1024 / 1024
        }
    
    def test_scale_scenario(self, scenario_name: str, initial_files: int, initial_rows_per_file: int, 
                           replacement_rows: int, target_file: str = "file_A"):
        """Test a specific scale scenario."""
        print(f"\n{'='*80}")
        print(f"SCALE TEST: {scenario_name}")
        print(f"{'='*80}")
        print(f"📊 Initial: {initial_files} files × {initial_rows_per_file:,} rows")
        print(f"📊 Replacing {target_file} with {replacement_rows:,} rows")
        
        # Setup
        dt = self.setup_large_table(initial_files, initial_rows_per_file)
        initial_resources = self.measure_system_resources()
        
        # Create replacement data
        replacement_data = create_large_file_data(target_file, replacement_rows)
        
        # Test merge approach
        print(f"\n🔄 Testing MERGE approach...")
        merge_start_resources = self.measure_system_resources()
        merge_result = self.test_merge_at_scale(dt, replacement_data, target_file)
        merge_end_resources = self.measure_system_resources()
        
        # Reset table for delete+append test
        dt = self.setup_large_table(initial_files, initial_rows_per_file)
        
        # Test delete+append approach
        print(f"\n🔄 Testing DELETE+APPEND approach...")
        delete_append_start_resources = self.measure_system_resources()
        delete_append_result = self.test_delete_append_at_scale(dt, replacement_data, target_file)
        delete_append_end_resources = self.measure_system_resources()
        
        # Store results
        result = {
            "scenario": scenario_name,
            "initial_files": initial_files,
            "initial_rows_per_file": initial_rows_per_file,
            "replacement_rows": replacement_rows,
            "merge_result": merge_result,
            "delete_append_result": delete_append_result,
            "resource_usage": {
                "initial": initial_resources,
                "merge_start": merge_start_resources,
                "merge_end": merge_end_resources,
                "delete_append_start": delete_append_start_resources,
                "delete_append_end": delete_append_end_resources
            }
        }
        
        self.results.append(result)
        self.analyze_scale_result(result)
        
        return result
    
    def test_merge_at_scale(self, dt: DeltaTable, replacement_data, target_file: str):
        """Test merge operation at scale."""
        start_time = time.time()
        initial_version = dt.version()
        
        try:
            result = dt.merge(
                source=replacement_data,
                predicate="false",
                source_alias="source",
                target_alias="target"
            ).when_not_matched_by_source_delete(
                predicate=f"target.file_id = '{target_file}'"
            ).when_not_matched_insert(
                {
                    "file_id": "source.file_id",
                    "customer_id": "source.customer_id",
                    "transaction_id": "source.transaction_id",
                    "amount": "source.amount",
                    "transaction_date": "source.transaction_date",
                    "processed_at": "source.processed_at"
                }
            ).execute()
            
            execution_time = time.time() - start_time
            dt_final = DeltaTable(self.table_path)
            
            return {
                "success": True,
                "execution_time": execution_time,
                "version_increment": dt_final.version() - initial_version,
                "final_rows": dt_final.to_pyarrow_table().num_rows,
                "metrics": result
            }
            
        except Exception as e:
            return {
                "success": False,
                "execution_time": time.time() - start_time,
                "error": str(e)
            }
    
    def test_delete_append_at_scale(self, dt: DeltaTable, replacement_data, target_file: str):
        """Test delete+append operation at scale."""
        total_start_time = time.time()
        initial_version = dt.version()
        
        try:
            # Delete phase
            delete_start = time.time()
            delete_result = dt.delete(predicate=f"file_id = '{target_file}'")
            delete_time = time.time() - delete_start
            
            dt_after_delete = DeltaTable(self.table_path)
            version_after_delete = dt_after_delete.version()
            
            # Append phase
            append_start = time.time()
            write_deltalake(self.table_path, replacement_data, mode="append")
            append_time = time.time() - append_start
            
            total_time = time.time() - total_start_time
            dt_final = DeltaTable(self.table_path)
            
            return {
                "success": True,
                "total_time": total_time,
                "delete_time": delete_time,
                "append_time": append_time,
                "version_increment": dt_final.version() - initial_version,
                "final_rows": dt_final.to_pyarrow_table().num_rows,
                "delete_metrics": delete_result
            }
            
        except Exception as e:
            return {
                "success": False,
                "total_time": time.time() - total_start_time,
                "error": str(e)
            }
    
    def analyze_scale_result(self, result: dict):
        """Analyze results from a scale test."""
        print(f"\n📊 SCALE ANALYSIS: {result['scenario']}")
        print("-" * 60)
        
        merge_result = result["merge_result"]
        delete_append_result = result["delete_append_result"]
        resources = result["resource_usage"]
        
        # Performance comparison
        if merge_result.get("success") and delete_append_result.get("success"):
            merge_time = merge_result["execution_time"]
            delete_append_time = delete_append_result["total_time"]
            
            print(f"⏱️  Performance:")
            print(f"  Merge: {merge_time:.3f}s")
            print(f"  Delete+Append: {delete_append_time:.3f}s")
            
            if merge_time < delete_append_time:
                print(f"  🏆 Merge is {delete_append_time/merge_time:.2f}x faster")
            else:
                print(f"  🏆 Delete+Append is {merge_time/delete_append_time:.2f}x faster")
        
        # Resource usage analysis
        print(f"\n💾 Resource Usage:")
        print(f"  Initial memory: {resources['initial']['memory_mb']:.1f} MB")
        print(f"  Peak memory (merge): {resources['merge_end']['memory_mb']:.1f} MB")
        print(f"  Peak memory (delete+append): {resources['delete_append_end']['memory_mb']:.1f} MB")
        print(f"  Final disk usage: {resources['delete_append_end']['disk_usage_mb']:.1f} MB")
        
        # Version management
        if merge_result.get("success") and delete_append_result.get("success"):
            print(f"\n📝 Version Management:")
            print(f"  Merge versions created: {merge_result['version_increment']}")
            print(f"  Delete+Append versions created: {delete_append_result['version_increment']}")
    
    def run_comprehensive_scale_tests(self):
        """Run comprehensive scale tests."""
        print("🚀 Starting Comprehensive Scale Analysis")
        print("="*80)
        
        # Test scenarios with increasing scale
        scenarios = [
            ("Small Scale", 5, 100, 150),           # 500 → 150 rows
            ("Medium Scale", 10, 1000, 1500),      # 10K → 1.5K rows  
            ("Large Scale", 20, 5000, 7500),       # 100K → 7.5K rows
            ("Very Large Scale", 50, 10000, 15000), # 500K → 15K rows
        ]
        
        for scenario_name, files, rows_per_file, replacement_rows in scenarios:
            try:
                self.test_scale_scenario(scenario_name, files, rows_per_file, replacement_rows)
            except Exception as e:
                print(f"❌ {scenario_name} failed: {e}")
        
        # Summary analysis
        self.print_scale_summary()
    
    def print_scale_summary(self):
        """Print summary of all scale tests."""
        print("\n" + "="*80)
        print("SCALE ANALYSIS SUMMARY")
        print("="*80)
        
        if not self.results:
            print("❌ No results to analyze")
            return
        
        print(f"\n📊 Performance Trends:")
        print(f"{'Scenario':<20} {'Merge (s)':<12} {'Del+App (s)':<12} {'Winner':<10}")
        print("-" * 60)
        
        merge_wins = 0
        delete_append_wins = 0
        
        for result in self.results:
            scenario = result["scenario"]
            merge_result = result["merge_result"]
            delete_append_result = result["delete_append_result"]
            
            if merge_result.get("success") and delete_append_result.get("success"):
                merge_time = merge_result["execution_time"]
                delete_append_time = delete_append_result["total_time"]
                
                if merge_time < delete_append_time:
                    winner = "Merge"
                    merge_wins += 1
                else:
                    winner = "Del+App"
                    delete_append_wins += 1
                
                print(f"{scenario:<20} {merge_time:<12.3f} {delete_append_time:<12.3f} {winner:<10}")
        
        print(f"\n🏆 Overall Winner: {'Merge' if merge_wins > delete_append_wins else 'Delete+Append'}")
        print(f"   Merge wins: {merge_wins}/{len(self.results)}")
        print(f"   Delete+Append wins: {delete_append_wins}/{len(self.results)}")
        
        print(f"\n⚠️  Potential Scale Issues:")
        print("  - Memory usage increases with data size")
        print("  - Delete+Append creates more versions (impacts query performance)")
        print("  - Concurrent operations more likely to conflict at scale")
        print("  - ACID guarantees become more critical with larger datasets")


def run_scale_analysis():
    """Run complete scale analysis."""
    analyzer = ScaleAnalysis()
    
    try:
        analyzer.run_comprehensive_scale_tests()
        
        print("\n" + "="*80)
        print("🎯 SCALE RECOMMENDATIONS")
        print("="*80)
        print("For large-scale ETL operations:")
        print("✅ MERGE is generally preferred because:")
        print("  - Better ACID guarantees at scale")
        print("  - Fewer version increments (cleaner history)")
        print("  - Better concurrency handling")
        print("  - More predictable performance characteristics")
        
        print("\n⚠️  Watch out for:")
        print("  - Memory usage with very large replacement datasets")
        print("  - File count growth (run OPTIMIZE regularly)")
        print("  - Concurrent operation conflicts")
        
    finally:
        analyzer.cleanup()


if __name__ == "__main__":
    run_scale_analysis()
