"""
DuckLake analysis with the same test scenarios as Delta Lake for direct comparison.
Uses DuckDB with the DuckLake extension for lakehouse operations.
"""

import os
import shutil
import time
import psutil
import random
from pathlib import Path
import pandas as pd
import duckdb
from delta_merge_examples.shared_data_generator import (
    generate_realistic_file_sizes, 
    generate_batch_data, 
    create_file_metadata,
    generate_transaction_data
)


class DuckLakeAnalysis:
    """Analyze DuckLake performance with the same scenarios as Delta Lake."""
    
    def __init__(self, lake_path: str = "./ducklake_test"):
        self.lake_path = lake_path
        self.data_path = f"{lake_path}.files"
        self.conn = None
        self.file_metadata = {}
        
    def setup_ducklake(self):
        """Initialize DuckLake connection and extension."""
        # Clean up any existing files/directories
        try:
            if os.path.exists(self.lake_path):
                if os.path.isfile(self.lake_path):
                    os.remove(self.lake_path)
                else:
                    shutil.rmtree(self.lake_path)
        except:
            pass
        try:
            if os.path.exists(self.data_path):
                shutil.rmtree(self.data_path)
        except:
            pass
        
        # Create DuckDB connection
        self.conn = duckdb.connect()
        
        # Install and load DuckLake extension
        self.conn.execute("INSTALL ducklake;")
        self.conn.execute("LOAD ducklake;")
        
        # Attach DuckLake database
        self.conn.execute(f"ATTACH 'ducklake:{self.lake_path}' AS my_ducklake;")
        self.conn.execute("USE my_ducklake;")
        
        print(f"✅ DuckLake initialized at {self.lake_path}")
    
    def cleanup(self):
        """Clean up resources."""
        try:
            if self.conn:
                self.conn.close()
        except:
            pass
        try:
            if os.path.exists(self.lake_path):
                if os.path.isfile(self.lake_path):
                    os.remove(self.lake_path)
                else:
                    shutil.rmtree(self.lake_path)
        except:
            pass
        try:
            if os.path.exists(self.data_path):
                shutil.rmtree(self.data_path)
        except:
            pass
    
    def create_table_schema(self):
        """Create the main table schema."""
        self.conn.execute("""
            CREATE TABLE demo (
                file_id VARCHAR,
                customer_id VARCHAR,
                transaction_id VARCHAR,
                amount DOUBLE,
                transaction_date TIMESTAMP,
                processed_at TIMESTAMP
            );
        """)
        print("✅ Created table schema")
    
    def setup_realistic_table_in_batches(self, total_files: int = 1000, total_rows: int = 50000, 
                                       batch_size: int = 50):
        """Create a realistic table by loading files in batches with skewed size distribution."""
        print(f"🏗️  Creating realistic DuckLake table: {total_files} files, {total_rows:,} total rows")
        print(f"📦 Loading in batches of {batch_size} files")
        
        # Generate realistic file size distribution
        file_sizes = generate_realistic_file_sizes(total_files, total_rows)
        
        # Create file metadata
        file_ids = [f"file_{i:04d}" for i in range(total_files)]
        self.file_metadata = create_file_metadata(file_ids, file_sizes)
        
        # Create table schema
        self.create_table_schema()
        
        # Load data in batches
        batch_count = 0
        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            batch_file_ids = file_ids[batch_start:batch_end]
            batch_file_sizes = file_sizes[batch_start:batch_end]
            
            print(f"  📦 Loading batch {batch_count + 1}: files {batch_start}-{batch_end-1}")
            
            # Generate batch data
            batch_df = generate_batch_data(batch_file_ids, batch_file_sizes)
            
            if not batch_df.empty:
                # Insert data using DuckDB
                self.conn.register('batch_data', batch_df)
                self.conn.execute("INSERT INTO demo SELECT * FROM batch_data;")
                self.conn.unregister('batch_data')
            
            batch_count += 1
        
        # Get final row count
        result = self.conn.execute("SELECT COUNT(*) FROM demo;").fetchone()
        total_actual_rows = result[0] if result else 0
        
        print(f"✅ Created realistic DuckLake table with {total_actual_rows:,} rows across {total_files} files")
        
        # Print distribution summary
        large_files = [f for f, meta in self.file_metadata.items() if meta['is_large']]
        small_files = [f for f, meta in self.file_metadata.items() if not meta['is_large']]
        print(f"📊 Distribution: {len(large_files)} large files, {len(small_files)} small files")
        
        return total_actual_rows
    
    def measure_system_resources(self):
        """Measure current system resource usage."""
        process = psutil.Process()
        return {
            "memory_mb": process.memory_info().rss / 1024 / 1024,
            "cpu_percent": process.cpu_percent(),
        }
    
    def test_append_operation(self):
        """Test appending a new large file."""
        new_file_id = f"file_append_test_{random.randint(9000, 9999)}"
        new_size = 5000  # Large file
        
        print(f"📊 Testing DuckLake APPEND: {new_file_id} ({new_size} rows)")
        
        start_resources = self.measure_system_resources()
        start_time = time.time()
        
        try:
            # Generate new data
            new_df = generate_transaction_data(new_file_id, new_size)
            
            # Insert using DuckDB
            self.conn.register('new_data', new_df)
            self.conn.execute("INSERT INTO demo SELECT * FROM new_data;")
            self.conn.unregister('new_data')
            
            execution_time = time.time() - start_time
            end_resources = self.measure_system_resources()
            
            # Get final row count
            result = self.conn.execute("SELECT COUNT(*) FROM demo;").fetchone()
            final_rows = result[0] if result else 0
            
            return {
                "success": True,
                "execution_time": execution_time,
                "final_rows": final_rows,
                "memory_delta": end_resources['memory_mb'] - start_resources['memory_mb'],
                "operation": "APPEND",
                "data_size": new_size
            }
            
        except Exception as e:
            return {
                "success": False,
                "execution_time": time.time() - start_time,
                "error": str(e),
                "operation": "APPEND"
            }
    
    def test_delete_operation(self):
        """Test deleting a large file."""
        # Find a large file to delete
        large_files = [f for f, meta in self.file_metadata.items() if meta['is_large']]
        if not large_files:
            return None
            
        target_file = random.choice(large_files)
        original_size = self.file_metadata[target_file]['size']
        
        print(f"📊 Testing DuckLake DELETE: {target_file} ({original_size} rows)")
        
        start_resources = self.measure_system_resources()
        start_time = time.time()
        
        try:
            # Get initial row count
            result = self.conn.execute("SELECT COUNT(*) FROM demo;").fetchone()
            initial_rows = result[0] if result else 0
            
            # Delete the file
            self.conn.execute(f"DELETE FROM demo WHERE file_id = '{target_file}';")
            
            execution_time = time.time() - start_time
            end_resources = self.measure_system_resources()
            
            # Get final row count
            result = self.conn.execute("SELECT COUNT(*) FROM demo;").fetchone()
            final_rows = result[0] if result else 0
            
            return {
                "success": True,
                "execution_time": execution_time,
                "final_rows": final_rows,
                "rows_deleted": initial_rows - final_rows,
                "memory_delta": end_resources['memory_mb'] - start_resources['memory_mb'],
                "operation": "DELETE",
                "data_size": original_size
            }
            
        except Exception as e:
            return {
                "success": False,
                "execution_time": time.time() - start_time,
                "error": str(e),
                "operation": "DELETE"
            }
    
    def test_update_operation(self):
        """Test updating a large file (delete + insert)."""
        # Find a large file to update
        large_files = [f for f, meta in self.file_metadata.items() if meta['is_large']]
        if not large_files:
            return None
            
        target_file = random.choice(large_files)
        original_size = self.file_metadata[target_file]['size']
        new_size = max(1000, int(original_size * random.uniform(0.8, 1.2)))
        
        print(f"📊 Testing DuckLake UPDATE: {target_file} ({original_size} → {new_size} rows)")
        
        start_resources = self.measure_system_resources()
        start_time = time.time()
        
        try:
            # Begin transaction for atomic update
            self.conn.execute("BEGIN TRANSACTION;")
            
            # Delete old data
            self.conn.execute(f"DELETE FROM demo WHERE file_id = '{target_file}';")
            
            # Insert new data
            new_df = generate_transaction_data(target_file, new_size)
            self.conn.register('update_data', new_df)
            self.conn.execute("INSERT INTO demo SELECT * FROM update_data;")
            self.conn.unregister('update_data')
            
            # Commit transaction
            self.conn.execute("COMMIT;")
            
            execution_time = time.time() - start_time
            end_resources = self.measure_system_resources()
            
            # Get final row count
            result = self.conn.execute("SELECT COUNT(*) FROM demo;").fetchone()
            final_rows = result[0] if result else 0
            
            return {
                "success": True,
                "execution_time": execution_time,
                "final_rows": final_rows,
                "memory_delta": end_resources['memory_mb'] - start_resources['memory_mb'],
                "operation": "UPDATE",
                "data_size": original_size,
                "new_size": new_size
            }
            
        except Exception as e:
            # Rollback on error
            try:
                self.conn.execute("ROLLBACK;")
            except:
                pass
            
            return {
                "success": False,
                "execution_time": time.time() - start_time,
                "error": str(e),
                "operation": "UPDATE"
            }

    def get_snapshot_info(self):
        """Get DuckLake snapshot information."""
        try:
            result = self.conn.execute("SELECT * FROM ducklake_snapshots('my_ducklake');").fetchall()
            return result
        except Exception as e:
            print(f"⚠️  Could not retrieve snapshot info: {e}")
            return []

    def analyze_operation_result(self, result: dict, operation_name: str):
        """Analyze results from a DuckLake operation."""
        print(f"\n📊 DUCKLAKE ANALYSIS: {operation_name}")
        print("-" * 60)

        if result.get("success"):
            print(f"✅ DuckLake {operation_name} Results:")
            print(f"  ⏱️  Execution time: {result['execution_time']:.3f}s")
            print(f"  📈 Final rows: {result['final_rows']:,}")
            print(f"  💾 Memory change: {result['memory_delta']:+.1f} MB")

            if 'rows_deleted' in result:
                print(f"  🗑️  Rows deleted: {result['rows_deleted']:,}")
            if 'new_size' in result:
                print(f"  🔄 Data change: {result['data_size']:,} → {result['new_size']:,} rows")
        else:
            print(f"❌ DuckLake {operation_name} failed: {result.get('error', 'Unknown error')}")

    def run_operation_comparisons(self):
        """Run comprehensive comparisons of DuckLake operations."""
        print("🚀 Starting DuckLake Operation Analysis")
        print("="*80)

        # Setup realistic table
        self.setup_ducklake()
        self.setup_realistic_table_in_batches(
            total_files=500,  # Smaller for faster testing
            total_rows=25000,
            batch_size=25
        )

        comparison_results = {}

        # Test 1: APPEND operation
        print(f"\n{'='*80}")
        print(f"DUCKLAKE TEST: APPEND Operation")
        print(f"{'='*80}")

        try:
            append_result = self.test_append_operation()
            if append_result:
                comparison_results['APPEND'] = append_result
                self.analyze_operation_result(append_result, "APPEND")
        except Exception as e:
            print(f"❌ APPEND test failed: {e}")

        # Test 2: DELETE operation
        print(f"\n{'='*80}")
        print(f"DUCKLAKE TEST: DELETE Operation")
        print(f"{'='*80}")

        try:
            delete_result = self.test_delete_operation()
            if delete_result:
                comparison_results['DELETE'] = delete_result
                self.analyze_operation_result(delete_result, "DELETE")
        except Exception as e:
            print(f"❌ DELETE test failed: {e}")

        # Test 3: UPDATE operation
        print(f"\n{'='*80}")
        print(f"DUCKLAKE TEST: UPDATE Operation")
        print(f"{'='*80}")

        try:
            update_result = self.test_update_operation()
            if update_result:
                comparison_results['UPDATE'] = update_result
                self.analyze_operation_result(update_result, "UPDATE")
        except Exception as e:
            print(f"❌ UPDATE test failed: {e}")

        # Show snapshot information
        print(f"\n{'='*80}")
        print(f"DUCKLAKE SNAPSHOT ANALYSIS")
        print(f"{'='*80}")

        snapshots = self.get_snapshot_info()
        if snapshots:
            print(f"📊 Total snapshots: {len(snapshots)}")
            for snapshot in snapshots[-5:]:  # Show last 5 snapshots
                snapshot_id, snapshot_time, schema_version, changes = snapshot
                print(f"  Snapshot {snapshot_id}: {snapshot_time} - {changes}")
        else:
            print("⚠️  No snapshot information available")

        # Print summary
        self.print_ducklake_summary(comparison_results)

        return comparison_results

    def print_ducklake_summary(self, results: dict):
        """Print comprehensive summary of DuckLake tests."""
        print("\n" + "="*80)
        print("DUCKLAKE ANALYSIS SUMMARY")
        print("="*80)

        if not results:
            print("❌ No results to analyze")
            return

        print(f"\n📊 DuckLake Performance Summary:")
        print(f"{'Operation':<12} {'Data Size':<12} {'Time (s)':<12} {'Memory Δ':<12} {'Status':<12}")
        print("-" * 70)

        for operation, result in results.items():
            if result.get('success'):
                data_size = result.get('data_size', 0)
                exec_time = result['execution_time']
                memory_delta = result['memory_delta']
                status = "SUCCESS"

                print(f"{operation:<12} {data_size:<12,} {exec_time:<12.3f} {memory_delta:<+12.1f} {status:<12}")
            else:
                print(f"{operation:<12} {'N/A':<12} {'FAILED':<12} {'N/A':<12} {'FAILED':<12}")

        print(f"\n🎯 DuckLake Key Insights:")
        print("1. 🗄️  DuckLake uses SQL database for metadata (faster metadata operations)")
        print("2. 🔄 All operations are transactional by default")
        print("3. 📊 Snapshot management is handled automatically")
        print("4. 💾 Memory usage patterns depend on SQL engine efficiency")
        print("5. ⚡ Performance benefits from SQL query optimization")

        print(f"\n✅ DuckLake Advantages:")
        print("- Familiar SQL interface for all operations")
        print("- Built-in transaction support")
        print("- Efficient metadata management in SQL database")
        print("- Time travel and snapshot queries")
        print("- No complex file-based metadata")


def run_ducklake_analysis():
    """Run complete DuckLake analysis."""
    analyzer = DuckLakeAnalysis()

    try:
        analyzer.run_operation_comparisons()

        print("\n" + "="*80)
        print("🎯 DUCKLAKE RECOMMENDATIONS")
        print("="*80)
        print("For lakehouse operations with DuckLake:")
        print("✅ DuckLake is excellent when:")
        print("  - You need familiar SQL interface")
        print("  - Metadata operations are frequent")
        print("  - You want built-in transaction support")
        print("  - You prefer database-managed consistency")
        print("  - You need efficient time travel queries")

        print("\n⚠️  Consider trade-offs:")
        print("  - Requires DuckDB as compute engine")
        print("  - SQL database dependency for metadata")
        print("  - Different ecosystem than Delta/Iceberg")
        print("  - Newer format with smaller ecosystem")

    finally:
        analyzer.cleanup()


if __name__ == "__main__":
    run_ducklake_analysis()
