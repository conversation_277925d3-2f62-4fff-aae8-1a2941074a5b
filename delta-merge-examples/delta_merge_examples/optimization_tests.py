"""
Performance optimization tests for Delta Lake and DuckLake.
Tests various optimization strategies based on profiling insights.
"""

import os
import shutil
import time
from pathlib import Path
import pyarrow as pa
import pyarrow.parquet as pq
import duckdb
from deltalake import DeltaTable, write_deltalake

from delta_merge_examples.shared_data_generator import (
    ParquetDatasetManager, 
    generate_transaction_data
)
from delta_merge_examples.performance_profiler import DeltaLakeProfiler


class OptimizationTests:
    """Test various optimization strategies for lakehouse operations."""
    
    def __init__(self):
        self.dataset_manager = ParquetDatasetManager()
        self.base_path = "./optimization_tests"
        self.results = {}
    
    def setup_test_data(self):
        """Set up test data for optimization experiments."""
        dataset_name = "optimization_test"
        
        if not self.dataset_manager.dataset_exists(dataset_name):
            print("📦 Generating optimization test dataset...")
            metadata = self.dataset_manager.generate_dataset(
                dataset_name=dataset_name,
                total_files=100,  # Smaller for faster testing
                total_rows=50000,
                large_file_ratio=1/20  # More large files for testing
            )
        else:
            metadata = self.dataset_manager.load_dataset_metadata(dataset_name)
        
        return metadata
    
    def test_delta_partitioning_optimization(self, metadata):
        """Test Delta Lake with partitioning by file_id."""
        print(f"\n{'='*80}")
        print("OPTIMIZATION TEST: Delta Lake Partitioning")
        print(f"{'='*80}")
        
        table_path = f"{self.base_path}/delta_partitioned"
        if os.path.exists(table_path):
            shutil.rmtree(table_path)
        
        profiler = DeltaLakeProfiler(table_path)
        
        # Load data with partitioning
        print("🔧 Creating partitioned Delta table...")
        batch_size = 20
        total_files = len(metadata['parquet_files'])
        
        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            batch_files = metadata['parquet_files'][batch_start:batch_end]
            parquet_paths = [f['parquet_path'] for f in batch_files]
            
            tables = [pq.read_table(path) for path in parquet_paths]
            combined_table = pa.concat_tables(tables)
            
            # Write with partitioning by file_id prefix
            mode = "overwrite" if batch_start == 0 else "append"
            write_deltalake(
                table_path, 
                combined_table, 
                mode=mode,
                partition_by=["file_id"]  # Partition by file_id
            )
        
        dt = DeltaTable(table_path)
        print(f"✅ Partitioned table created with {dt.to_pyarrow_table().num_rows:,} rows")
        
        # Test MERGE performance on partitioned table
        large_files = [f for f in metadata['parquet_files'] if f['is_large']]
        target_file_info = large_files[0]
        target_file = target_file_info['file_id']
        new_size = max(1000, int(target_file_info['row_count'] * 0.9))
        
        new_df = generate_transaction_data(target_file, new_size)
        new_data = pa.Table.from_pandas(new_df)
        
        print(f"🎯 Testing MERGE on partitioned table: {target_file}")
        result = profiler.profile_merge_operation(dt, new_data, target_file)
        profiler.print_profile_summary()
        
        self.results['delta_partitioned'] = profiler.current_profile
        
        # Cleanup
        shutil.rmtree(table_path)
        
        return result
    
    def test_delta_zorder_optimization(self, metadata):
        """Test Delta Lake with Z-ORDER optimization."""
        print(f"\n{'='*80}")
        print("OPTIMIZATION TEST: Delta Lake Z-ORDER")
        print(f"{'='*80}")
        
        table_path = f"{self.base_path}/delta_zorder"
        if os.path.exists(table_path):
            shutil.rmtree(table_path)
        
        profiler = DeltaLakeProfiler(table_path)
        
        # Create regular table first
        print("🔧 Creating Delta table for Z-ORDER optimization...")
        batch_size = 20
        total_files = len(metadata['parquet_files'])
        
        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            batch_files = metadata['parquet_files'][batch_start:batch_end]
            parquet_paths = [f['parquet_path'] for f in batch_files]
            
            tables = [pq.read_table(path) for path in parquet_paths]
            combined_table = pa.concat_tables(tables)
            
            mode = "overwrite" if batch_start == 0 else "append"
            write_deltalake(table_path, combined_table, mode=mode)
        
        dt = DeltaTable(table_path)
        print(f"✅ Base table created with {dt.to_pyarrow_table().num_rows:,} rows")
        
        # Apply OPTIMIZE with Z-ORDER
        print("🔧 Applying OPTIMIZE with Z-ORDER BY file_id...")
        start_time = time.time()
        try:
            # Note: Z-ORDER might not be available in all Delta Lake versions
            dt.optimize.z_order(["file_id"])
            optimize_time = time.time() - start_time
            print(f"✅ Z-ORDER optimization completed in {optimize_time:.3f}s")
        except Exception as e:
            print(f"⚠️  Z-ORDER not available: {e}")
            # Fall back to regular OPTIMIZE
            dt.optimize.compact()
            optimize_time = time.time() - start_time
            print(f"✅ Regular OPTIMIZE completed in {optimize_time:.3f}s")
        
        # Test MERGE performance on optimized table
        large_files = [f for f in metadata['parquet_files'] if f['is_large']]
        target_file_info = large_files[1] if len(large_files) > 1 else large_files[0]
        target_file = target_file_info['file_id']
        new_size = max(1000, int(target_file_info['row_count'] * 0.9))
        
        new_df = generate_transaction_data(target_file, new_size)
        new_data = pa.Table.from_pandas(new_df)
        
        print(f"🎯 Testing MERGE on optimized table: {target_file}")
        result = profiler.profile_merge_operation(dt, new_data, target_file)
        profiler.print_profile_summary()
        
        self.results['delta_zorder'] = profiler.current_profile
        
        # Cleanup
        shutil.rmtree(table_path)
        
        return result
    
    def test_ducklake_indexing_optimization(self, metadata):
        """Test DuckLake with indexing optimization."""
        print(f"\n{'='*80}")
        print("OPTIMIZATION TEST: DuckLake Indexing")
        print(f"{'='*80}")
        
        # Setup DuckLake with indexing
        ducklake_path = f"{self.base_path}/ducklake_indexed"
        if os.path.exists(ducklake_path):
            if os.path.isfile(ducklake_path):
                os.remove(ducklake_path)
            else:
                shutil.rmtree(ducklake_path)
        
        conn = duckdb.connect()
        conn.execute("INSTALL ducklake;")
        conn.execute("LOAD ducklake;")
        conn.execute(f"ATTACH 'ducklake:{ducklake_path}' AS indexed_lake;")
        conn.execute("USE indexed_lake;")
        
        # Create table with standard schema (DuckLake doesn't support PRIMARY KEY)
        conn.execute("""
            CREATE TABLE demo (
                file_id VARCHAR,
                customer_id VARCHAR,
                transaction_id VARCHAR,
                amount DOUBLE,
                transaction_date TIMESTAMP,
                processed_at TIMESTAMP
            );
        """)

        # Create index on file_id for faster lookups
        try:
            conn.execute("CREATE INDEX idx_file_id ON demo(file_id);")
            print("✅ Created index on file_id")
        except Exception as e:
            print(f"⚠️  Could not create index: {e}")
        
        # Load data
        print("🔧 Loading data into indexed DuckLake table...")
        batch_size = 20
        total_files = len(metadata['parquet_files'])
        
        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            batch_files = metadata['parquet_files'][batch_start:batch_end]
            
            for file_info in batch_files:
                parquet_path = file_info['parquet_path']
                conn.execute(f"INSERT INTO demo SELECT * FROM read_parquet('{parquet_path}');")
        
        result = conn.execute("SELECT COUNT(*) FROM demo;").fetchone()
        total_rows = result[0] if result else 0
        print(f"✅ Indexed table created with {total_rows:,} rows")
        
        # Test performance with index
        large_files = [f for f in metadata['parquet_files'] if f['is_large']]
        target_file_info = large_files[0]
        target_file = target_file_info['file_id']
        new_size = max(1000, int(target_file_info['row_count'] * 0.9))
        
        new_df = generate_transaction_data(target_file, new_size)
        conn.register('new_data', new_df)
        
        print(f"🎯 Testing indexed DELETE+INSERT: {target_file}")
        
        # Profile the transaction
        start_time = time.time()
        conn.execute("BEGIN TRANSACTION;")
        conn.execute(f"DELETE FROM demo WHERE file_id = '{target_file}';")
        conn.execute("INSERT INTO demo SELECT * FROM new_data;")
        conn.execute("COMMIT;")
        execution_time = time.time() - start_time
        
        print(f"⏱️  Indexed operation time: {execution_time:.3f}s")
        
        self.results['ducklake_indexed'] = {
            'operation': 'DuckLake Indexed Transaction',
            'execution_time': execution_time
        }
        
        # Cleanup
        conn.unregister('new_data')
        conn.close()
        
        return execution_time
    
    def test_delta_bloom_filter_optimization(self, metadata):
        """Test Delta Lake with bloom filter optimization."""
        print(f"\n{'='*80}")
        print("OPTIMIZATION TEST: Delta Lake Bloom Filters")
        print(f"{'='*80}")
        
        table_path = f"{self.base_path}/delta_bloom"
        if os.path.exists(table_path):
            shutil.rmtree(table_path)
        
        profiler = DeltaLakeProfiler(table_path)
        
        # Create table with bloom filter configuration
        print("🔧 Creating Delta table with bloom filter configuration...")
        batch_size = 20
        total_files = len(metadata['parquet_files'])
        
        for batch_start in range(0, total_files, batch_size):
            batch_end = min(batch_start + batch_size, total_files)
            batch_files = metadata['parquet_files'][batch_start:batch_end]
            parquet_paths = [f['parquet_path'] for f in batch_files]
            
            tables = [pq.read_table(path) for path in parquet_paths]
            combined_table = pa.concat_tables(tables)
            
            mode = "overwrite" if batch_start == 0 else "append"
            
            # Configure data skipping stats for file_id column
            write_deltalake(
                table_path,
                combined_table,
                mode=mode,
                configuration={
                    "delta.dataSkippingStatsColumns": "file_id"
                }
            )
        
        dt = DeltaTable(table_path)
        print(f"✅ Data skipping table created with {dt.to_pyarrow_table().num_rows:,} rows")

        # Test MERGE performance with data skipping
        large_files = [f for f in metadata['parquet_files'] if f['is_large']]
        target_file_info = large_files[2] if len(large_files) > 2 else large_files[0]
        target_file = target_file_info['file_id']
        new_size = max(1000, int(target_file_info['row_count'] * 0.9))
        
        new_df = generate_transaction_data(target_file, new_size)
        new_data = pa.Table.from_pandas(new_df)
        
        print(f"🎯 Testing MERGE with data skipping: {target_file}")
        result = profiler.profile_merge_operation(dt, new_data, target_file)
        profiler.print_profile_summary()
        
        self.results['delta_data_skipping'] = profiler.current_profile
        
        # Cleanup
        shutil.rmtree(table_path)
        
        return result
    
    def compare_optimization_results(self):
        """Compare all optimization results."""
        print(f"\n{'='*80}")
        print("OPTIMIZATION COMPARISON RESULTS")
        print(f"{'='*80}")
        
        if not self.results:
            print("❌ No optimization results to compare")
            return
        
        print(f"{'Optimization':<25} {'Time (s)':<12} {'Improvement':<15}")
        print("-" * 60)
        
        baseline_time = None
        for opt_name, result in self.results.items():
            exec_time = result.get('execution_time', 0)
            
            if baseline_time is None:
                baseline_time = exec_time
                improvement = "Baseline"
            else:
                if exec_time > 0:
                    improvement = f"{baseline_time/exec_time:.2f}x faster"
                else:
                    improvement = "N/A"
            
            print(f"{opt_name:<25} {exec_time:<12.3f} {improvement:<15}")
        
        print(f"\n🎯 OPTIMIZATION INSIGHTS:")
        print("1. 🗂️  Partitioning helps with file pruning for targeted operations")
        print("2. 🔍 Z-ORDER/OPTIMIZE reduces file count and improves scan performance")
        print("3. 📊 Data skipping stats enable efficient file pruning")
        print("4. 🗄️  Database indexes (DuckLake) provide fastest lookups")
        print("5. ⚡ Native operations remain fastest for simple cases")


def run_optimization_tests():
    """Run comprehensive optimization tests."""
    tester = OptimizationTests()
    
    try:
        # Setup test data
        metadata = tester.setup_test_data()
        
        # Test various optimizations
        print("🚀 Starting optimization tests...")
        
        # Test 1: Partitioning
        tester.test_delta_partitioning_optimization(metadata)
        
        # Test 2: Z-ORDER optimization
        tester.test_delta_zorder_optimization(metadata)
        
        # Test 3: Data skipping
        tester.test_delta_bloom_filter_optimization(metadata)
        
        # Test 4: DuckLake indexing
        tester.test_ducklake_indexing_optimization(metadata)
        
        # Compare results
        tester.compare_optimization_results()
        
    except Exception as e:
        print(f"❌ Optimization tests failed: {e}")
        raise
    
    finally:
        # Cleanup
        if os.path.exists(tester.base_path):
            shutil.rmtree(tester.base_path)


if __name__ == "__main__":
    run_optimization_tests()
