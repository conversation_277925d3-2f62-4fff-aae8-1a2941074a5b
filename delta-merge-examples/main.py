"""
Main script to run Delta Lake merge examples and analysis.

This script demonstrates:
1. Basic merge operations for file replacement
2. ACID transaction analysis with Delta log inspection
3. Performance analysis at different scales
4. Comparison between merge and delete+append approaches
"""

import sys
import argparse

from delta_merge_examples.merge_examples import run_all_examples
from delta_merge_examples.acid_analysis import run_acid_analysis
from delta_merge_examples.scale_analysis import run_scale_analysis
from delta_merge_examples.comparison import run_comparison


def main():
    """Main entry point."""
    parser = argparse.ArgumentParser(
        description="Delta Lake merge examples and analysis",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python main.py --all                    # Run all examples and analysis
  python main.py --basic                  # Run basic merge examples
  python main.py --acid                   # Run ACID analysis with log inspection
  python main.py --scale                  # Run scale analysis
  python main.py --comparison             # Run merge vs delete+append comparison
        """
    )

    parser.add_argument("--all", action="store_true",
                       help="Run all examples and analysis")
    parser.add_argument("--basic", action="store_true",
                       help="Run basic merge examples")
    parser.add_argument("--acid", action="store_true",
                       help="Run ACID analysis with Delta log inspection")
    parser.add_argument("--scale", action="store_true",
                       help="Run scale analysis")
    parser.add_argument("--comparison", action="store_true",
                       help="Run merge vs delete+append comparison")

    args = parser.parse_args()

    # If no specific option is chosen, show help
    if not any([args.all, args.basic, args.acid, args.scale, args.comparison]):
        parser.print_help()
        return

    print("🚀 Delta Lake File Replacement Examples")
    print("="*80)
    print("This demo shows different approaches for replacing data in Delta tables")
    print("when new versions of files arrive in ETL scenarios.")
    print("="*80)

    try:
        if args.all or args.basic:
            print("\n🎯 Running basic merge examples...")
            run_all_examples()

        if args.all or args.acid:
            print("\n🎯 Running ACID analysis with Delta log inspection...")
            run_acid_analysis()

        if args.all or args.comparison:
            print("\n🎯 Running merge vs delete+append comparison...")
            run_comparison()

        if args.all or args.scale:
            print("\n🎯 Running scale analysis...")
            run_scale_analysis()

        print("\n" + "="*80)
        print("✅ All requested examples completed successfully!")
        print("="*80)

        print("\n📋 KEY TAKEAWAYS:")
        print("1. 🔄 MERGE with dummy join (predicate='false') is ideal for file replacement")
        print("2. 🔒 MERGE provides better ACID guarantees than delete+append")
        print("3. 📊 MERGE creates fewer Delta table versions")
        print("4. ⚡ Performance varies by scale, but MERGE is generally preferred")
        print("5. 🔍 Delta log inspection shows the transaction-level differences")

        print("\n🎯 RECOMMENDATION:")
        print("For ETL file replacement scenarios, use the MERGE approach with:")
        print("  - predicate='false' (dummy join)")
        print("  - when_not_matched_by_source_delete() for old data")
        print("  - when_not_matched_insert() for new data")

    except KeyboardInterrupt:
        print("\n\n⚠️  Interrupted by user")
    except Exception as e:
        print(f"\n\n❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
