{"dataset_name": "optimization_test", "total_files": 100, "total_rows": 50000, "large_file_ratio": 0.05, "file_metadata": {"file_0000": {"size": 108, "is_large": false, "index": 0}, "file_0001": {"size": 114, "is_large": false, "index": 1}, "file_0002": {"size": 183, "is_large": false, "index": 2}, "file_0003": {"size": 160, "is_large": false, "index": 3}, "file_0004": {"size": 132, "is_large": false, "index": 4}, "file_0005": {"size": 113, "is_large": false, "index": 5}, "file_0006": {"size": 182, "is_large": false, "index": 6}, "file_0007": {"size": 159, "is_large": false, "index": 7}, "file_0008": {"size": 141, "is_large": false, "index": 8}, "file_0009": {"size": 112, "is_large": false, "index": 9}, "file_0010": {"size": 7952, "is_large": true, "index": 10}, "file_0011": {"size": 204, "is_large": false, "index": 11}, "file_0012": {"size": 112, "is_large": false, "index": 12}, "file_0013": {"size": 137, "is_large": false, "index": 13}, "file_0014": {"size": 182, "is_large": false, "index": 14}, "file_0015": {"size": 130, "is_large": false, "index": 15}, "file_0016": {"size": 169, "is_large": false, "index": 16}, "file_0017": {"size": 160, "is_large": false, "index": 17}, "file_0018": {"size": 204, "is_large": false, "index": 18}, "file_0019": {"size": 133, "is_large": false, "index": 19}, "file_0020": {"size": 123, "is_large": false, "index": 20}, "file_0021": {"size": 170, "is_large": false, "index": 21}, "file_0022": {"size": 152, "is_large": false, "index": 22}, "file_0023": {"size": 168, "is_large": false, "index": 23}, "file_0024": {"size": 149, "is_large": false, "index": 24}, "file_0025": {"size": 199, "is_large": false, "index": 25}, "file_0026": {"size": 137, "is_large": false, "index": 26}, "file_0027": {"size": 155, "is_large": false, "index": 27}, "file_0028": {"size": 184, "is_large": false, "index": 28}, "file_0029": {"size": 137, "is_large": false, "index": 29}, "file_0030": {"size": 170, "is_large": false, "index": 30}, "file_0031": {"size": 203, "is_large": false, "index": 31}, "file_0032": {"size": 205, "is_large": false, "index": 32}, "file_0033": {"size": 113, "is_large": false, "index": 33}, "file_0034": {"size": 189, "is_large": false, "index": 34}, "file_0035": {"size": 170, "is_large": false, "index": 35}, "file_0036": {"size": 179, "is_large": false, "index": 36}, "file_0037": {"size": 119, "is_large": false, "index": 37}, "file_0038": {"size": 6921, "is_large": true, "index": 38}, "file_0039": {"size": 7087, "is_large": true, "index": 39}, "file_0040": {"size": 7524, "is_large": true, "index": 40}, "file_0041": {"size": 142, "is_large": false, "index": 41}, "file_0042": {"size": 196, "is_large": false, "index": 42}, "file_0043": {"size": 145, "is_large": false, "index": 43}, "file_0044": {"size": 119, "is_large": false, "index": 44}, "file_0045": {"size": 148, "is_large": false, "index": 45}, "file_0046": {"size": 6727, "is_large": true, "index": 46}, "file_0047": {"size": 126, "is_large": false, "index": 47}, "file_0048": {"size": 137, "is_large": false, "index": 48}, "file_0049": {"size": 203, "is_large": false, "index": 49}, "file_0050": {"size": 194, "is_large": false, "index": 50}, "file_0051": {"size": 152, "is_large": false, "index": 51}, "file_0052": {"size": 150, "is_large": false, "index": 52}, "file_0053": {"size": 195, "is_large": false, "index": 53}, "file_0054": {"size": 123, "is_large": false, "index": 54}, "file_0055": {"size": 167, "is_large": false, "index": 55}, "file_0056": {"size": 167, "is_large": false, "index": 56}, "file_0057": {"size": 138, "is_large": false, "index": 57}, "file_0058": {"size": 110, "is_large": false, "index": 58}, "file_0059": {"size": 133, "is_large": false, "index": 59}, "file_0060": {"size": 175, "is_large": false, "index": 60}, "file_0061": {"size": 184, "is_large": false, "index": 61}, "file_0062": {"size": 199, "is_large": false, "index": 62}, "file_0063": {"size": 137, "is_large": false, "index": 63}, "file_0064": {"size": 192, "is_large": false, "index": 64}, "file_0065": {"size": 179, "is_large": false, "index": 65}, "file_0066": {"size": 197, "is_large": false, "index": 66}, "file_0067": {"size": 158, "is_large": false, "index": 67}, "file_0068": {"size": 166, "is_large": false, "index": 68}, "file_0069": {"size": 170, "is_large": false, "index": 69}, "file_0070": {"size": 180, "is_large": false, "index": 70}, "file_0071": {"size": 129, "is_large": false, "index": 71}, "file_0072": {"size": 207, "is_large": false, "index": 72}, "file_0073": {"size": 122, "is_large": false, "index": 73}, "file_0074": {"size": 194, "is_large": false, "index": 74}, "file_0075": {"size": 107, "is_large": false, "index": 75}, "file_0076": {"size": 202, "is_large": false, "index": 76}, "file_0077": {"size": 113, "is_large": false, "index": 77}, "file_0078": {"size": 181, "is_large": false, "index": 78}, "file_0079": {"size": 108, "is_large": false, "index": 79}, "file_0080": {"size": 116, "is_large": false, "index": 80}, "file_0081": {"size": 162, "is_large": false, "index": 81}, "file_0082": {"size": 133, "is_large": false, "index": 82}, "file_0083": {"size": 127, "is_large": false, "index": 83}, "file_0084": {"size": 162, "is_large": false, "index": 84}, "file_0085": {"size": 112, "is_large": false, "index": 85}, "file_0086": {"size": 178, "is_large": false, "index": 86}, "file_0087": {"size": 188, "is_large": false, "index": 87}, "file_0088": {"size": 180, "is_large": false, "index": 88}, "file_0089": {"size": 114, "is_large": false, "index": 89}, "file_0090": {"size": 190, "is_large": false, "index": 90}, "file_0091": {"size": 198, "is_large": false, "index": 91}, "file_0092": {"size": 125, "is_large": false, "index": 92}, "file_0093": {"size": 161, "is_large": false, "index": 93}, "file_0094": {"size": 198, "is_large": false, "index": 94}, "file_0095": {"size": 207, "is_large": false, "index": 95}, "file_0096": {"size": 148, "is_large": false, "index": 96}, "file_0097": {"size": 169, "is_large": false, "index": 97}, "file_0098": {"size": 162, "is_large": false, "index": 98}, "file_0099": {"size": 112, "is_large": false, "index": 99}}, "parquet_files": [{"file_id": "file_0000", "parquet_path": "test_datasets/optimization_test/file_0000.parquet", "row_count": 108, "is_large": false}, {"file_id": "file_0001", "parquet_path": "test_datasets/optimization_test/file_0001.parquet", "row_count": 114, "is_large": false}, {"file_id": "file_0002", "parquet_path": "test_datasets/optimization_test/file_0002.parquet", "row_count": 183, "is_large": false}, {"file_id": "file_0003", "parquet_path": "test_datasets/optimization_test/file_0003.parquet", "row_count": 160, "is_large": false}, {"file_id": "file_0004", "parquet_path": "test_datasets/optimization_test/file_0004.parquet", "row_count": 132, "is_large": false}, {"file_id": "file_0005", "parquet_path": "test_datasets/optimization_test/file_0005.parquet", "row_count": 113, "is_large": false}, {"file_id": "file_0006", "parquet_path": "test_datasets/optimization_test/file_0006.parquet", "row_count": 182, "is_large": false}, {"file_id": "file_0007", "parquet_path": "test_datasets/optimization_test/file_0007.parquet", "row_count": 159, "is_large": false}, {"file_id": "file_0008", "parquet_path": "test_datasets/optimization_test/file_0008.parquet", "row_count": 141, "is_large": false}, {"file_id": "file_0009", "parquet_path": "test_datasets/optimization_test/file_0009.parquet", "row_count": 112, "is_large": false}, {"file_id": "file_0010", "parquet_path": "test_datasets/optimization_test/file_0010.parquet", "row_count": 7952, "is_large": true}, {"file_id": "file_0011", "parquet_path": "test_datasets/optimization_test/file_0011.parquet", "row_count": 204, "is_large": false}, {"file_id": "file_0012", "parquet_path": "test_datasets/optimization_test/file_0012.parquet", "row_count": 112, "is_large": false}, {"file_id": "file_0013", "parquet_path": "test_datasets/optimization_test/file_0013.parquet", "row_count": 137, "is_large": false}, {"file_id": "file_0014", "parquet_path": "test_datasets/optimization_test/file_0014.parquet", "row_count": 182, "is_large": false}, {"file_id": "file_0015", "parquet_path": "test_datasets/optimization_test/file_0015.parquet", "row_count": 130, "is_large": false}, {"file_id": "file_0016", "parquet_path": "test_datasets/optimization_test/file_0016.parquet", "row_count": 169, "is_large": false}, {"file_id": "file_0017", "parquet_path": "test_datasets/optimization_test/file_0017.parquet", "row_count": 160, "is_large": false}, {"file_id": "file_0018", "parquet_path": "test_datasets/optimization_test/file_0018.parquet", "row_count": 204, "is_large": false}, {"file_id": "file_0019", "parquet_path": "test_datasets/optimization_test/file_0019.parquet", "row_count": 133, "is_large": false}, {"file_id": "file_0020", "parquet_path": "test_datasets/optimization_test/file_0020.parquet", "row_count": 123, "is_large": false}, {"file_id": "file_0021", "parquet_path": "test_datasets/optimization_test/file_0021.parquet", "row_count": 170, "is_large": false}, {"file_id": "file_0022", "parquet_path": "test_datasets/optimization_test/file_0022.parquet", "row_count": 152, "is_large": false}, {"file_id": "file_0023", "parquet_path": "test_datasets/optimization_test/file_0023.parquet", "row_count": 168, "is_large": false}, {"file_id": "file_0024", "parquet_path": "test_datasets/optimization_test/file_0024.parquet", "row_count": 149, "is_large": false}, {"file_id": "file_0025", "parquet_path": "test_datasets/optimization_test/file_0025.parquet", "row_count": 199, "is_large": false}, {"file_id": "file_0026", "parquet_path": "test_datasets/optimization_test/file_0026.parquet", "row_count": 137, "is_large": false}, {"file_id": "file_0027", "parquet_path": "test_datasets/optimization_test/file_0027.parquet", "row_count": 155, "is_large": false}, {"file_id": "file_0028", "parquet_path": "test_datasets/optimization_test/file_0028.parquet", "row_count": 184, "is_large": false}, {"file_id": "file_0029", "parquet_path": "test_datasets/optimization_test/file_0029.parquet", "row_count": 137, "is_large": false}, {"file_id": "file_0030", "parquet_path": "test_datasets/optimization_test/file_0030.parquet", "row_count": 170, "is_large": false}, {"file_id": "file_0031", "parquet_path": "test_datasets/optimization_test/file_0031.parquet", "row_count": 203, "is_large": false}, {"file_id": "file_0032", "parquet_path": "test_datasets/optimization_test/file_0032.parquet", "row_count": 205, "is_large": false}, {"file_id": "file_0033", "parquet_path": "test_datasets/optimization_test/file_0033.parquet", "row_count": 113, "is_large": false}, {"file_id": "file_0034", "parquet_path": "test_datasets/optimization_test/file_0034.parquet", "row_count": 189, "is_large": false}, {"file_id": "file_0035", "parquet_path": "test_datasets/optimization_test/file_0035.parquet", "row_count": 170, "is_large": false}, {"file_id": "file_0036", "parquet_path": "test_datasets/optimization_test/file_0036.parquet", "row_count": 179, "is_large": false}, {"file_id": "file_0037", "parquet_path": "test_datasets/optimization_test/file_0037.parquet", "row_count": 119, "is_large": false}, {"file_id": "file_0038", "parquet_path": "test_datasets/optimization_test/file_0038.parquet", "row_count": 6921, "is_large": true}, {"file_id": "file_0039", "parquet_path": "test_datasets/optimization_test/file_0039.parquet", "row_count": 7087, "is_large": true}, {"file_id": "file_0040", "parquet_path": "test_datasets/optimization_test/file_0040.parquet", "row_count": 7524, "is_large": true}, {"file_id": "file_0041", "parquet_path": "test_datasets/optimization_test/file_0041.parquet", "row_count": 142, "is_large": false}, {"file_id": "file_0042", "parquet_path": "test_datasets/optimization_test/file_0042.parquet", "row_count": 196, "is_large": false}, {"file_id": "file_0043", "parquet_path": "test_datasets/optimization_test/file_0043.parquet", "row_count": 145, "is_large": false}, {"file_id": "file_0044", "parquet_path": "test_datasets/optimization_test/file_0044.parquet", "row_count": 119, "is_large": false}, {"file_id": "file_0045", "parquet_path": "test_datasets/optimization_test/file_0045.parquet", "row_count": 148, "is_large": false}, {"file_id": "file_0046", "parquet_path": "test_datasets/optimization_test/file_0046.parquet", "row_count": 6727, "is_large": true}, {"file_id": "file_0047", "parquet_path": "test_datasets/optimization_test/file_0047.parquet", "row_count": 126, "is_large": false}, {"file_id": "file_0048", "parquet_path": "test_datasets/optimization_test/file_0048.parquet", "row_count": 137, "is_large": false}, {"file_id": "file_0049", "parquet_path": "test_datasets/optimization_test/file_0049.parquet", "row_count": 203, "is_large": false}, {"file_id": "file_0050", "parquet_path": "test_datasets/optimization_test/file_0050.parquet", "row_count": 194, "is_large": false}, {"file_id": "file_0051", "parquet_path": "test_datasets/optimization_test/file_0051.parquet", "row_count": 152, "is_large": false}, {"file_id": "file_0052", "parquet_path": "test_datasets/optimization_test/file_0052.parquet", "row_count": 150, "is_large": false}, {"file_id": "file_0053", "parquet_path": "test_datasets/optimization_test/file_0053.parquet", "row_count": 195, "is_large": false}, {"file_id": "file_0054", "parquet_path": "test_datasets/optimization_test/file_0054.parquet", "row_count": 123, "is_large": false}, {"file_id": "file_0055", "parquet_path": "test_datasets/optimization_test/file_0055.parquet", "row_count": 167, "is_large": false}, {"file_id": "file_0056", "parquet_path": "test_datasets/optimization_test/file_0056.parquet", "row_count": 167, "is_large": false}, {"file_id": "file_0057", "parquet_path": "test_datasets/optimization_test/file_0057.parquet", "row_count": 138, "is_large": false}, {"file_id": "file_0058", "parquet_path": "test_datasets/optimization_test/file_0058.parquet", "row_count": 110, "is_large": false}, {"file_id": "file_0059", "parquet_path": "test_datasets/optimization_test/file_0059.parquet", "row_count": 133, "is_large": false}, {"file_id": "file_0060", "parquet_path": "test_datasets/optimization_test/file_0060.parquet", "row_count": 175, "is_large": false}, {"file_id": "file_0061", "parquet_path": "test_datasets/optimization_test/file_0061.parquet", "row_count": 184, "is_large": false}, {"file_id": "file_0062", "parquet_path": "test_datasets/optimization_test/file_0062.parquet", "row_count": 199, "is_large": false}, {"file_id": "file_0063", "parquet_path": "test_datasets/optimization_test/file_0063.parquet", "row_count": 137, "is_large": false}, {"file_id": "file_0064", "parquet_path": "test_datasets/optimization_test/file_0064.parquet", "row_count": 192, "is_large": false}, {"file_id": "file_0065", "parquet_path": "test_datasets/optimization_test/file_0065.parquet", "row_count": 179, "is_large": false}, {"file_id": "file_0066", "parquet_path": "test_datasets/optimization_test/file_0066.parquet", "row_count": 197, "is_large": false}, {"file_id": "file_0067", "parquet_path": "test_datasets/optimization_test/file_0067.parquet", "row_count": 158, "is_large": false}, {"file_id": "file_0068", "parquet_path": "test_datasets/optimization_test/file_0068.parquet", "row_count": 166, "is_large": false}, {"file_id": "file_0069", "parquet_path": "test_datasets/optimization_test/file_0069.parquet", "row_count": 170, "is_large": false}, {"file_id": "file_0070", "parquet_path": "test_datasets/optimization_test/file_0070.parquet", "row_count": 180, "is_large": false}, {"file_id": "file_0071", "parquet_path": "test_datasets/optimization_test/file_0071.parquet", "row_count": 129, "is_large": false}, {"file_id": "file_0072", "parquet_path": "test_datasets/optimization_test/file_0072.parquet", "row_count": 207, "is_large": false}, {"file_id": "file_0073", "parquet_path": "test_datasets/optimization_test/file_0073.parquet", "row_count": 122, "is_large": false}, {"file_id": "file_0074", "parquet_path": "test_datasets/optimization_test/file_0074.parquet", "row_count": 194, "is_large": false}, {"file_id": "file_0075", "parquet_path": "test_datasets/optimization_test/file_0075.parquet", "row_count": 107, "is_large": false}, {"file_id": "file_0076", "parquet_path": "test_datasets/optimization_test/file_0076.parquet", "row_count": 202, "is_large": false}, {"file_id": "file_0077", "parquet_path": "test_datasets/optimization_test/file_0077.parquet", "row_count": 113, "is_large": false}, {"file_id": "file_0078", "parquet_path": "test_datasets/optimization_test/file_0078.parquet", "row_count": 181, "is_large": false}, {"file_id": "file_0079", "parquet_path": "test_datasets/optimization_test/file_0079.parquet", "row_count": 108, "is_large": false}, {"file_id": "file_0080", "parquet_path": "test_datasets/optimization_test/file_0080.parquet", "row_count": 116, "is_large": false}, {"file_id": "file_0081", "parquet_path": "test_datasets/optimization_test/file_0081.parquet", "row_count": 162, "is_large": false}, {"file_id": "file_0082", "parquet_path": "test_datasets/optimization_test/file_0082.parquet", "row_count": 133, "is_large": false}, {"file_id": "file_0083", "parquet_path": "test_datasets/optimization_test/file_0083.parquet", "row_count": 127, "is_large": false}, {"file_id": "file_0084", "parquet_path": "test_datasets/optimization_test/file_0084.parquet", "row_count": 162, "is_large": false}, {"file_id": "file_0085", "parquet_path": "test_datasets/optimization_test/file_0085.parquet", "row_count": 112, "is_large": false}, {"file_id": "file_0086", "parquet_path": "test_datasets/optimization_test/file_0086.parquet", "row_count": 178, "is_large": false}, {"file_id": "file_0087", "parquet_path": "test_datasets/optimization_test/file_0087.parquet", "row_count": 188, "is_large": false}, {"file_id": "file_0088", "parquet_path": "test_datasets/optimization_test/file_0088.parquet", "row_count": 180, "is_large": false}, {"file_id": "file_0089", "parquet_path": "test_datasets/optimization_test/file_0089.parquet", "row_count": 114, "is_large": false}, {"file_id": "file_0090", "parquet_path": "test_datasets/optimization_test/file_0090.parquet", "row_count": 190, "is_large": false}, {"file_id": "file_0091", "parquet_path": "test_datasets/optimization_test/file_0091.parquet", "row_count": 198, "is_large": false}, {"file_id": "file_0092", "parquet_path": "test_datasets/optimization_test/file_0092.parquet", "row_count": 125, "is_large": false}, {"file_id": "file_0093", "parquet_path": "test_datasets/optimization_test/file_0093.parquet", "row_count": 161, "is_large": false}, {"file_id": "file_0094", "parquet_path": "test_datasets/optimization_test/file_0094.parquet", "row_count": 198, "is_large": false}, {"file_id": "file_0095", "parquet_path": "test_datasets/optimization_test/file_0095.parquet", "row_count": 207, "is_large": false}, {"file_id": "file_0096", "parquet_path": "test_datasets/optimization_test/file_0096.parquet", "row_count": 148, "is_large": false}, {"file_id": "file_0097", "parquet_path": "test_datasets/optimization_test/file_0097.parquet", "row_count": 169, "is_large": false}, {"file_id": "file_0098", "parquet_path": "test_datasets/optimization_test/file_0098.parquet", "row_count": 162, "is_large": false}, {"file_id": "file_0099", "parquet_path": "test_datasets/optimization_test/file_0099.parquet", "row_count": 112, "is_large": false}]}