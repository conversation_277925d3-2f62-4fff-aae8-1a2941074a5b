# Delta Lake File Replacement Examples

This package demonstrates different approaches for replacing data in Delta tables when new versions of files arrive in ETL scenarios. It focuses on the common use case where you need to replace all data associated with a specific file ID.

## Overview

The examples compare two main approaches:

1. **MERGE with dummy join** (Recommended)
2. **DELETE + APPEND** (Simpler but less robust)

## Key Features

- 🔄 **Complete file replacement examples** with different data sizes
- 🔍 **Delta log inspection** to understand transaction behavior
- 📊 **ACID transaction analysis** comparing both approaches
- ⚡ **Performance testing** at different scales
- 📈 **Resource usage monitoring** and analysis

## Installation

This project uses `uv` for dependency management:

```bash
# Install dependencies
uv sync

# Activate virtual environment
source .venv/bin/activate  # On Unix/macOS
# or
.venv\Scripts\activate     # On Windows
```

## Usage

Run the main script with different options:

```bash
# Run all examples and analysis
python main.py --all

# Run specific examples
python main.py --basic      # Basic merge examples
python main.py --acid       # ACID analysis with Delta log inspection
python main.py --scale      # Scale analysis
python main.py --comparison # Performance comparison
```

## Examples Included

### 1. Basic Merge Examples (`--basic`)

Demonstrates file replacement scenarios:
- Same number of rows (3 → 3)
- Fewer rows (3 → 1)
- More rows (3 → 6)
- Empty file (3 → 0)

### 2. ACID Analysis (`--acid`)

Shows how each approach affects:
- **Atomicity**: Single vs multiple transactions
- **Consistency**: Intermediate states and data integrity
- **Isolation**: Concurrent operation handling
- **Durability**: Transaction log behavior

Includes detailed Delta log inspection showing:
- Commit entries
- File add/remove operations
- Operation metadata and metrics

### 3. Scale Analysis (`--scale`)

Tests performance at different scales:
- Small: 500 total rows
- Medium: 10K total rows
- Large: 100K total rows
- Very Large: 500K total rows

### 4. Performance Comparison (`--comparison`)

Direct comparison of merge vs delete+append approaches with:
- Execution time analysis
- Resource usage monitoring
- Transaction overhead comparison

## Key Findings

### MERGE Approach (Recommended)

```python
result = dt.merge(
    source=new_data,
    predicate="false",  # Dummy join - never matches
    source_alias="source",
    target_alias="target"
).when_not_matched_by_source_delete(
    predicate="target.file_id = 'file_A'"
).when_not_matched_insert({
    "file_id": "source.file_id",
    "customer_id": "source.customer_id",
    # ... other fields
}).execute()
```

**Advantages:**
- ✅ Single atomic transaction
- ✅ Better ACID guarantees
- ✅ Single version increment
- ✅ Better concurrency handling

### DELETE + APPEND Approach

```python
# Step 1: Delete
delete_result = dt.delete(predicate="file_id = 'file_A'")

# Step 2: Append
write_deltalake(table_path, new_data, mode="append")
```

**Advantages:**
- ✅ Simpler conceptual model
- ✅ More explicit operations

**Disadvantages:**
- ❌ Two separate transactions
- ❌ Risk of partial failure
- ❌ Creates two table versions
- ❌ Intermediate inconsistent state

## Delta Log Analysis

The examples show how each approach affects the Delta transaction log:

**MERGE creates:**
- 1 commit entry
- Remove actions for old files
- Add actions for new files
- Single version increment

**DELETE + APPEND creates:**
- 2 commit entries
- First transaction: Remove actions only
- Second transaction: Add actions only
- Two version increments

## Recommendations

For ETL file replacement scenarios:

🎯 **Use MERGE when:**
- ACID guarantees are critical
- Concurrent operations are common
- Clean version history is important
- Data consistency is paramount

🎯 **Use DELETE + APPEND when:**
- Operations are serialized (no concurrency)
- Simplicity is more important than atomicity
- You have robust error handling/retry logic

## Project Structure

```
delta_merge_examples/
├── delta_merge_examples/
│   ├── __init__.py
│   ├── data_generator.py      # Sample data generation
│   ├── merge_examples.py      # Basic merge examples
│   ├── acid_analysis.py       # ACID analysis with log inspection
│   ├── scale_analysis.py      # Performance at scale
│   └── comparison.py          # Direct comparison
├── main.py                    # Main entry point
├── README.md
└── pyproject.toml
```

## Dependencies

- `deltalake`: Delta Lake Python bindings
- `pyarrow`: Arrow data processing
- `pandas`: Data manipulation
- `psutil`: System resource monitoring

## Contributing

This is an educational example package. Feel free to extend it with additional scenarios or analysis.